import datetime

import streamlit as st
from datetime import datetime, timedelta

from constant import CHOOSE_CAT_LIST
from db_manager import DBManager
from crawler_manager import update_news_data_ui, get_crawl_status_summary, get_last_crawl_errors


def show_news():
    st.markdown("## 新闻汇总")
    db = DBManager()
    db.init_database()
    # 自定义CSS样式
    st.markdown("""
    <style>
        .news-card {
            background-color: #f0f2f6;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .news-title {
            color: #1f77b4;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .news-meta {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .news-content {
            color: #333;
            font-size: 16px;
            line-height: 1.6;
        }
        .news-source {
            color: #1f77b4;
            text-decoration: none;
        }
        .news-tag {
            background-color: #1f77b4;
            color: white;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 12px;
            display: inline-block;
            margin-right: 5px;
        }
    </style>
    """, unsafe_allow_html=True)

    # 侧边栏筛选
    with st.sidebar:
        st.header("筛选选项")

        # 品种/类别筛选
        categories = CHOOSE_CAT_LIST
        selected_category = st.selectbox("选择类别", categories)

        # 时间筛选
        st.subheader("时间筛选")
        time_mode = st.radio(
            "筛选模式",
            ["预设范围", "自定义日期"],
            horizontal=True
        )

        if time_mode == "预设范围":
            time_filter = st.selectbox(
                "时间范围",
                ["全部", "今天", "最近三天", "最近一周", "最近一个月"]
            )
            # 设置默认的开始和结束时间（用于显示信息）
            start_date = None
            end_date = None
        else:
            # 自定义日期选择
            end_date_default = datetime.now().date()
            start_date_default = end_date_default - timedelta(days=7)

            col1, col2 = st.columns(2)
            with col1:
                start_date = st.date_input(
                    "开始日期",
                    start_date_default,
                    format="YYYY-MM-DD"
                )
            with col2:
                end_date = st.date_input(
                    "结束日期",
                    end_date_default,
                    format="YYYY-MM-DD"
                )

            # 验证日期范围
            if start_date > end_date:
                st.error("开始日期不能晚于结束日期")
            else:
                st.success(f"时间范围: {start_date} 至 {end_date}")

            time_filter = "自定义"

        # 排序方式
        sort_by = st.radio("排序方式", ["时间降序", "时间升序"])

    news_data = db.load_article_list()

    # 根据筛选条件过滤数据
    filtered_news = news_data.copy()

    # 类别筛选
    # if selected_category != "全部":
    #     filtered_news = [n for n in filtered_news if n.cat == selected_category]

    # 时间筛选
    now = datetime.now()
    if time_filter == "今天":
        filtered_news = [n for n in filtered_news if (now - datetime.strptime(n.published_at, '%Y-%m-%d %H:%M:%S')).days == 0]
    elif time_filter == "最近三天":
        filtered_news = [n for n in filtered_news if (now - datetime.strptime(n.published_at, '%Y-%m-%d %H:%M:%S')).days <= 3]
    elif time_filter == "最近一周":
        filtered_news = [n for n in filtered_news if (now - datetime.strptime(n.published_at, '%Y-%m-%d %H:%M:%S')).days <= 7]
    elif time_filter == "最近一个月":
        filtered_news = [n for n in filtered_news if (now - datetime.strptime(n.published_at, '%Y-%m-%d %H:%M:%S')).days <= 30]
    elif time_filter == "自定义" and start_date and end_date:
        # 自定义日期范围筛选
        start_datetime = datetime.combine(start_date, datetime.min.time())
        end_datetime = datetime.combine(end_date, datetime.max.time())
        filtered_news = [n for n in filtered_news if start_datetime <= datetime.strptime(n.published_at, '%Y-%m-%d %H:%M:%S') <= end_datetime]

    # 排序
    filtered_news.sort(key=lambda x: datetime.strptime(x.published_at, '%Y-%m-%d %H:%M:%S'), reverse=(sort_by == "时间降序"))

    # 显示统计信息和爬取状态
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("新闻总数", len(filtered_news))
    with col2:
        st.metric("最新更新", datetime.strptime(filtered_news[0].published_at, '%Y-%m-%d %H:%M:%S').strftime("%H:%M") if filtered_news else "无")
    with col3:
        st.metric("数据来源", len(set([n.source for n in filtered_news])))
    with col4:
        # 显示爬取状态
        crawl_status = get_crawl_status_summary()
        if crawl_status["has_errors"]:
            st.metric("⚠️ 爬取状态", f"{crawl_status['failed_sources']} 个源异常", delta_color="inverse")
        else:
            st.metric("✅ 爬取状态", "正常")

    # 如果有爬取错误，显示警告信息
    crawl_errors = get_last_crawl_errors()
    if crawl_errors:
        st.warning(f"⚠️ 数据更新异常：{len(crawl_errors)} 个数据源爬取失败，可能影响数据完整性")
        with st.expander("查看详细错误信息", expanded=False):
            for source_name, error_info in crawl_errors.items():
                st.error(f"**{source_name}**: {error_info.get('message', '未知错误')}")
                if error_info.get("error_type"):
                    st.text(f"错误类型: {error_info['error_type']}")
                if error_info.get("timestamp"):
                    st.text(f"发生时间: {error_info['timestamp']}")
                st.divider()

    st.divider()
    update_news_data_ui()
    # 添加数据更新和刷新功能
    st.divider()
    # 卡片视图 - 两列布局
    for idx, news in enumerate(filtered_news):
        with st.container():
            st.markdown(f"""
            <div class="news-card">
                <div class="news-title">{news.title}</div>
                <div class="news-meta">
                    <span class="news-tag">{news.cat}</span>
                    {datetime.strptime(news.published_at, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d %H:%M')} |
                    <a href="{news.url}" target="_blank" class="news-source">{news.source}</a>
                </div>
                <div class="news-content">{news.content}</div>
            </div>
            """, unsafe_allow_html=True)
    # 卡片视图 - 两列布局
    # col1, col2 = st.columns(2)
    # for idx, news in enumerate(filtered_news):
    #     with col1 if idx % 2 == 0 else col2:
    #         with st.container():
    #             st.markdown(f"""
    #             <div class="news-card">
    #                 <div class="news-title">{news.title}</div>
    #                 <div class="news-meta">
    #                     <span class="news-tag">{news.cat}</span>
    #                     {datetime.strptime(news.published_at, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d %H:%M')} |
    #                     <a href="{news.url}" target="_blank" class="news-source">{news.source}</a>
    #                 </div>
    #                 <div class="news-content">{news.content}</div>
    #             </div>
    #             """, unsafe_allow_html=True)
